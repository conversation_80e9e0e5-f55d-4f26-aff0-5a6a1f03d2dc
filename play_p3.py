# 播放p3格式的音频文件
import struct
import numpy as np
import sounddevice as sd
import argparse
import subprocess
import tempfile
import os

def play_p3_file(input_file):
    """
    播放p3格式的音频文件
    p3格式: [1字节类型, 1字节保留, 2字节长度, Opus数据]
    """
    sample_rate = 16000  # 采样率固定为16000Hz
    channels = 1  # 单声道

    # 提取opus数据包
    opus_packets = []

    with open(input_file, 'rb') as f:
        while True:
            # 读取头部 (4字节)
            header = f.read(4)
            if not header or len(header) < 4:
                break

            # 解析头部
            packet_type, reserved, data_len = struct.unpack('>BBH', header)

            # 读取Opus数据
            opus_data = f.read(data_len)
            if not opus_data or len(opus_data) < data_len:
                break

            opus_packets.append(opus_data)

    if not opus_packets:
        print("没有找到有效的音频数据")
        return

    # 创建临时opus文件
    with tempfile.NamedTemporaryFile(suffix='.opus', delete=False) as temp_opus:
        temp_opus_path = temp_opus.name
        # 写入opus数据包
        for packet in opus_packets:
            temp_opus.write(packet)

    # 创建临时wav文件
    with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_wav:
        temp_wav_path = temp_wav.name

    try:
        print(f"正在播放: {input_file}")

        # 处理真正的Opus编码数据
        print("正在解码Opus数据包进行播放...")

        # 创建临时文件来解码Opus数据
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_wav:
            temp_wav_path = temp_wav.name

        try:
            # 使用convert_p3_to_audio的解码功能
            from convert_p3_to_audio import decode_with_opusdec, create_silence_file

            # 尝试解码Opus数据包
            decode_with_opusdec(opus_packets, temp_wav_path, sample_rate)

            # 读取解码后的音频文件
            import soundfile as sf
            audio_data, sr = sf.read(temp_wav_path, dtype='int16')

            # 播放音频
            sd.play(audio_data, sr)
            sd.wait()  # 等待播放完成

        except Exception as e:
            print(f"解码失败: {e}")
            print("使用备用播放方案...")
            # 备用方案：播放静音
            duration = len(opus_packets) * 0.06
            silence_data = np.zeros(int(sample_rate * duration), dtype=np.int16)
            sd.play(silence_data, sample_rate)
            sd.wait()

        finally:
            # 清理临时文件
            if os.path.exists(temp_wav_path):
                os.unlink(temp_wav_path)

    except KeyboardInterrupt:
        print("\n播放已停止")
    except Exception as e:
        print(f"播放出错: {e}")
    finally:
        # 清理临时文件
        if os.path.exists(temp_opus_path):
            os.unlink(temp_opus_path)
        if os.path.exists(temp_wav_path):
            os.unlink(temp_wav_path)
        print("播放完成")

def main():
    parser = argparse.ArgumentParser(description='播放p3格式的音频文件')
    parser.add_argument('input_file', help='输入的p3文件路径')
    args = parser.parse_args()
    
    play_p3_file(args.input_file)

if __name__ == "__main__":
    main() 
