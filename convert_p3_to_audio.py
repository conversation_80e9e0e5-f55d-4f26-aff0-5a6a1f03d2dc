import struct
import sys
import numpy as np
from tqdm import tqdm
import soundfile as sf
import subprocess
import tempfile
import os


def decode_p3_to_audio(input_file, output_file):
    sample_rate = 16000
    channels = 1

    # Extract opus packets from p3 file
    opus_packets = []

    with open(input_file, "rb") as f:
        f.seek(0, 2)
        total_size = f.tell()
        f.seek(0)

        with tqdm(total=total_size, unit="B", unit_scale=True) as pbar:
            while True:
                header = f.read(4)
                if not header or len(header) < 4:
                    break

                pkt_type, reserved, opus_len = struct.unpack(">BBH", header)
                opus_data = f.read(opus_len)
                if len(opus_data) != opus_len:
                    break

                opus_packets.append(opus_data)
                pbar.update(4 + opus_len)

    if not opus_packets:
        raise ValueError("No valid audio data found")

    # 由于当前P3文件中存储的是PCM数据，我们可以直接处理
    print("注意：当前处理PCM数据（非Opus编码）")

    # 将所有PCM数据包合并
    all_audio_data = []
    for packet in opus_packets:
        # 每个包包含的是16位PCM数据
        pcm_data = np.frombuffer(packet, dtype=np.int16)
        all_audio_data.append(pcm_data)

    # 合并所有音频数据
    if all_audio_data:
        audio_data = np.concatenate(all_audio_data)

        # 直接写入音频文件
        sf.write(output_file, audio_data, sample_rate, subtype="PCM_16")
        print(f"成功转换为: {output_file}")
    else:
        print("没有找到音频数据")


if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python convert_p3_to_audio.py <input.p3> <output.wav>")
        sys.exit(1)

    decode_p3_to_audio(sys.argv[1], sys.argv[2])
