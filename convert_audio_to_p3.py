# convert audio files to protocol v3 stream
import librosa
import struct
import sys
import tqdm
import numpy as np
import argparse
import pyloudnorm as pyln
import subprocess
import tempfile
import os

def parse_and_write_opus_packets(opus_raw_data, output_file):
    """
    解析原始Opus数据并写入P3格式
    """
    # 简化的Opus数据包解析
    # 实际的Opus数据包有复杂的头部结构，这里使用简化方法

    # 对于64kbps，60ms帧，预期每个数据包64-200字节
    min_packet_size = 64
    max_packet_size = 200

    # 估算数据包数量
    estimated_packets = len(opus_raw_data) // 100  # 假设平均100字节/包

    with open(output_file, 'wb') as f:
        offset = 0
        packet_count = 0

        while offset < len(opus_raw_data):
            # 简化的数据包大小计算
            remaining = len(opus_raw_data) - offset

            # 动态调整数据包大小
            if remaining > max_packet_size:
                packet_size = max_packet_size
            elif remaining > min_packet_size:
                packet_size = remaining
            else:
                packet_size = remaining

            if packet_size == 0:
                break

            opus_packet = opus_raw_data[offset:offset + packet_size]
            offset += packet_size

            # 写入P3格式的头部
            packet_type = 0  # 音频数据包类型
            reserved = 0     # 保留字节
            data_len = len(opus_packet)

            # 写入4字节头部: [类型, 保留, 长度(2字节大端序)]
            header = struct.pack('>BBH', packet_type, reserved, data_len)
            f.write(header)

            # 写入opus编码数据
            f.write(opus_packet)
            packet_count += 1

        print(f"Generated {packet_count} opus packets, average size: {len(opus_raw_data)/packet_count:.1f} bytes")

def encode_with_opusenc(wav_path, output_file, sample_rate):
    """
    使用opusenc作为备用编码方案
    """
    opusenc_path = os.path.join(os.path.dirname(__file__), 'opus-tools', 'opusenc.exe')

    with tempfile.NamedTemporaryFile(suffix='.opus', delete=False) as temp_opus:
        temp_opus_path = temp_opus.name

    try:
        # 使用opusenc编码
        cmd = [
            opusenc_path,
            '--bitrate', '64',
            '--framesize', '60',
            wav_path,
            temp_opus_path
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            raise RuntimeError(f"Opusenc failed: {result.stderr}")

        # 读取Ogg Opus文件并提取Opus数据包
        # 这需要解析Ogg容器格式，这里使用简化方法
        with open(temp_opus_path, 'rb') as opus_file:
            ogg_data = opus_file.read()

        # 简化：跳过Ogg头部，提取Opus数据
        # 实际实现需要正确解析Ogg页面结构
        opus_data_start = find_opus_data_start(ogg_data)
        if opus_data_start > 0:
            opus_raw_data = ogg_data[opus_data_start:]
            parse_and_write_opus_packets(opus_raw_data, output_file)
        else:
            raise RuntimeError("无法从Ogg文件中提取Opus数据")

    finally:
        if os.path.exists(temp_opus_path):
            os.unlink(temp_opus_path)

def find_opus_data_start(ogg_data):
    """
    在Ogg文件中查找Opus数据的开始位置
    这是一个简化的实现
    """
    # 查找OpusHead标识
    opus_head = b'OpusHead'
    head_pos = ogg_data.find(opus_head)
    if head_pos == -1:
        return 0

    # 跳过头部信息，查找实际的音频数据
    # 这是一个非常简化的方法
    return min(head_pos + 1000, len(ogg_data) // 4)

def encode_audio_to_opus(input_file, output_file, target_lufs=None):
    # Load audio file using librosa
    audio, sample_rate = librosa.load(input_file, sr=None, mono=False, dtype=np.float32)

    # Convert to mono if stereo
    if audio.ndim == 2:
        audio = librosa.to_mono(audio)

    if target_lufs is not None:
        print("Note: Automatic loudness adjustment is enabled, which may cause", file=sys.stderr)
        print("      audio distortion. If the input audio has already been ", file=sys.stderr)
        print("      loudness-adjusted or if the input audio is TTS audio, ", file=sys.stderr)
        print("      please use the `-d` parameter to disable loudness adjustment.", file=sys.stderr)
        meter = pyln.Meter(sample_rate)
        current_loudness = meter.integrated_loudness(audio)
        audio = pyln.normalize.loudness(audio, current_loudness, target_lufs)
        print(f"Adjusted loudness: {current_loudness:.1f} LUFS -> {target_lufs} LUFS")

    # Convert sample rate to 16000Hz if necessary
    target_sample_rate = 16000
    if sample_rate != target_sample_rate:
        audio = librosa.resample(audio, orig_sr=sample_rate, target_sr=target_sample_rate)
        sample_rate = target_sample_rate

    # Convert audio data back to int16 after processing
    audio = (audio * 32767).astype(np.int16)

    # Create temporary WAV file for ffmpeg
    with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_wav:
        temp_wav_path = temp_wav.name
        # Write WAV header and data
        import wave
        with wave.open(temp_wav_path, 'wb') as wav_file:
            wav_file.setnchannels(1)  # mono
            wav_file.setsampwidth(2)  # 16-bit
            wav_file.setframerate(target_sample_rate)
            wav_file.writeframes(audio.tobytes())

    # Create temporary opus file
    with tempfile.NamedTemporaryFile(suffix='.opus', delete=False) as temp_opus:
        temp_opus_path = temp_opus.name

    try:
        # Use ffmpeg to encode to opus
        ffmpeg_path = os.path.join(os.path.dirname(__file__), 'ffmpeg', 'ffmpeg-7.1.1-essentials_build', 'bin', 'ffmpeg.exe')
        cmd = [
            ffmpeg_path, '-y', '-i', temp_wav_path,
            '-c:a', 'libopus',
            '-b:a', '64k',
            '-frame_duration', '60',
            temp_opus_path
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            raise RuntimeError(f"FFmpeg failed: {result.stderr}")

        # 使用FFmpeg生成真正的Opus编码数据包
        # 创建临时文件用于存储原始Opus数据包
        with tempfile.NamedTemporaryFile(suffix='.opus', delete=False) as temp_raw_opus:
            temp_raw_opus_path = temp_raw_opus.name

        try:
            # 使用FFmpeg编码为原始Opus数据包格式
            cmd = [
                ffmpeg_path, '-y', '-i', temp_wav_path,
                '-c:a', 'libopus',
                '-b:a', '64k',
                '-frame_duration', '60',
                '-f', 'data',  # 输出原始数据
                temp_raw_opus_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode != 0:
                print(f"FFmpeg raw opus failed: {result.stderr}")
                # 备用方案：使用opusenc
                encode_with_opusenc(temp_wav_path, output_file, target_sample_rate)
                return

            # 读取原始Opus数据
            with open(temp_raw_opus_path, 'rb') as opus_file:
                opus_raw_data = opus_file.read()

            # 将原始Opus数据分割成数据包
            # 对于64kbps，60ms帧，每个数据包大约64-200字节
            parse_and_write_opus_packets(opus_raw_data, output_file)

        finally:
            # 清理临时文件
            if os.path.exists(temp_raw_opus_path):
                os.unlink(temp_raw_opus_path)

    finally:
        # Clean up temporary files
        if os.path.exists(temp_wav_path):
            os.unlink(temp_wav_path)
        if os.path.exists(temp_opus_path):
            os.unlink(temp_opus_path)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Convert audio to Opus with loudness normalization')
    parser.add_argument('input_file', help='Input audio file')
    parser.add_argument('output_file', help='Output .opus file')
    parser.add_argument('-l', '--lufs', type=float, default=-16.0,
                       help='Target loudness in LUFS (default: -16)')
    parser.add_argument('-d', '--disable-loudnorm', action='store_true',
                       help='Disable loudness normalization')
    args = parser.parse_args()

    target_lufs = None if args.disable_loudnorm else args.lufs
    encode_audio_to_opus(args.input_file, args.output_file, target_lufs)